{"name": "click2cater-service", "version": "1.0.0", "description": "E-carting", "main": "dist/index.js", "scripts": {"clean": "rimraf dist/", "lint": "eslint src/**/*.ts --fix", "tsc": "tsc --project ./tsconfig.json", "prettier-all": "prettier --write \"src/**/*.+(ts|tsx|js|css|json)\"", "precompile": "npm run prettier-all && npm run lint && npm run clean", "compile": "npm run tsc", "build": "npm run compile", "postbuild": "powershell -Command \"if (Test-Path 'dist') { Get-ChildItem -Path 'dist' -Recurse -Name '*.spec.js' | ForEach-Object { Remove-Item -Path (Join-Path 'dist' $_) -Force } }\" && powershell -Command \"if (Test-Path 'apidoc.json') { Copy-Item 'apidoc.json' 'dist/apidoc.json' -Force }\" && apidoc -i dist/controllers -o dist/public/apidoc", "watch": "tsc -w -p ./src -p ./tsconfig.json", "dev": "nodemon ./src/index.ts", "dev:debug": "export DEBUG_PORT=9229 && nodemon ./src/index.ts", "start": "node ./dist/index.js", "typeorm": "node --require ts-node/register ./node_modules/typeorm/cli.js -f src/typeorm/config/ormconfig.ts", "lint-check": "eslint src/**/*.ts"}, "prettier": {"trailingComma": "none", "singleQuote": true, "printWidth": 120, "tabWidth": 4, "semi": true}, "devDependencies": {"@types/bcrypt": "^6.0.0", "@types/cookie-parser": "^1.4.9", "@types/cors": "^2.8.19", "@types/express": "^4.17.17", "@types/jsonwebtoken": "^9.0.10", "@types/morgan": "^1.9.10", "@types/node": "^20.4.9", "@types/pg": "^8.15.5", "@typescript-eslint/eslint-plugin": "^8.44.0", "@typescript-eslint/parser": "^8.44.0", "eslint": "^8.57.1", "nodemon": "^3.0.1", "prettier": "^2.8.8", "rimraf": "^5.0.1", "ts-node": "^10.9.1", "typescript": "^5.1.6"}, "keywords": ["Shopify", "API", "Client"], "author": "<PERSON><PERSON>", "license": "ISC", "dependencies": {"apidoc": "^1.1.0", "bcrypt": "^5.1.1", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "cryptr": "^6.3.0", "dotenv": "^16.3.1", "express": "^4.18.2", "joi": "^17.12.2", "json2xls": "^0.1.2", "jsonwebtoken": "^9.0.1", "morgan": "^1.10.0", "multer": "^2.0.2", "pg": "^8.11.2", "reflect-metadata": "^0.1.13", "sequelize": "^6.32.1", "winston": "^3.10.0"}, "repository": {"type": "git", "url": "git+https://github.com/punitDT/whitelotus_assignment.git"}, "bugs": {"url": "https://github.com/punitDT/whitelotus_assignment/issues"}, "homepage": "https://github.com/punitDT/whitelotus_assignment/blob/main/README.md"}