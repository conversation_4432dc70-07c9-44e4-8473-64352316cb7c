import express, { Router } from 'express';
import fs from 'fs';
import { UploadConstant } from '../utils/upload_constant';
const router = Router();


router.get('/invoice/:name', (req, res) => {
    const fileName = req.params.name;
    try {
        const file = fs.readFileSync(`${UploadConstant.UPLOAD_DIR_INVOICE}/${fileName}`);
        // res.writeHead(200, { 'Content-Type': 'image/jpg' });
        res.end(file, 'binary');
    } catch (err: any) {
        res.status(400).send(err.message);
    }
});

export default router;
