import Joi from 'joi';

export const createInvoiceSchema = Joi.object({
    invoice_number: Joi.string().required(),
    client_name: Joi.string().required(),
    amount: Joi.number().required(),
    status: Joi.string().valid('PENDING', 'PAID', 'OVERDUE').required(),
    due_date: Joi.date().required(),
    invoice_file_name: Joi.string().required(),
    invoice_file_url: Joi.string().required()
});
